import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { AcademicCapIcon, CalendarIcon, TrophyIcon } from '@heroicons/react/24/outline'

const Education = () => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  })

  const educationData = [
    {
      degree: 'Master of Computer Applications (MCA)',
      institution: 'University Name',
      duration: '2022 - 2024',
      description: 'Specialized in Software Engineering, Database Management, and Advanced Programming. Completed major projects in web development and mobile applications.',
      grade: 'CGPA: 8.5/10',
      icon: <AcademicCapIcon className="w-6 h-6" />
    },
    {
      degree: 'Bachelor of Computer Applications (BCA)',
      institution: 'College Name',
      duration: '2019 - 2022',
      description: 'Foundation in computer science fundamentals, programming languages, and software development methodologies.',
      grade: 'Percentage: 85%',
      icon: <AcademicCapIcon className="w-6 h-6" />
    },
    {
      degree: 'Higher Secondary (12th)',
      institution: 'School Name',
      duration: '2018 - 2019',
      description: 'Science stream with Mathematics, Physics, and Chemistry.',
      grade: 'Percentage: 88%',
      icon: <AcademicCapIcon className="w-6 h-6" />
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6 }
    }
  }

  return (
    <section id="education" className="py-20 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <motion.h2 variants={itemVariants} className="section-title">
            Education
          </motion.h2>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-blue-500"></div>
            
            <div className="space-y-8">
              {educationData.map((item, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="timeline-item"
                >
                  {/* Timeline Dot */}
                  <div className="timeline-dot flex items-center justify-center">
                    {item.icon}
                  </div>
                  
                  {/* Content */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                    className="timeline-content"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                      <h3 className="text-xl font-semibold text-gray-800 mb-1 sm:mb-0">
                        {item.degree}
                      </h3>
                      <div className="flex items-center text-blue-600 text-sm font-medium">
                        <CalendarIcon className="w-4 h-4 mr-1" />
                        {item.duration}
                      </div>
                    </div>
                    
                    <h4 className="text-blue-600 font-medium mb-3">
                      {item.institution}
                    </h4>
                    
                    <p className="text-gray-600 leading-relaxed mb-4">
                      {item.description}
                    </p>
                    
                    <div className="flex items-center">
                      <TrophyIcon className="w-5 h-5 text-yellow-500 mr-2" />
                      <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {item.grade}
                      </span>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>
          
          {/* Achievements Section */}
          <motion.div
            variants={itemVariants}
            className="mt-16 bg-white rounded-2xl p-8 shadow-lg"
          >
            <h3 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
              Academic Achievements
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <TrophyIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Dean's List</h4>
                  <p className="text-gray-600 text-sm">Consistently maintained high academic performance</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <TrophyIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Best Project Award</h4>
                  <p className="text-gray-600 text-sm">Recognition for innovative final year project</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <TrophyIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Technical Symposium</h4>
                  <p className="text-gray-600 text-sm">Winner in coding competition</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <TrophyIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Research Paper</h4>
                  <p className="text-gray-600 text-sm">Published research on machine learning applications</p>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Education
