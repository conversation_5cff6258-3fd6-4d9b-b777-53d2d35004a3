import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { 
  ShoppingCartIcon, 
  ClipboardDocumentListIcon, 
  ChartBarIcon,
  CodeBracketIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline'

const Projects = () => {
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: true
  })

  const projects = [
    {
      title: 'E-Commerce Platform',
      description: 'Full-stack e-commerce application with user authentication, product management, and payment integration.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      icon: <ShoppingCartIcon className="w-12 h-12" />,
      gradient: 'from-blue-500 to-cyan-500',
      github: '#',
      demo: '#'
    },
    {
      title: 'Task Management System',
      description: 'Collaborative task management application with real-time updates, team collaboration, and progress tracking.',
      technologies: ['Java', 'Spring Boot', 'MySQL', 'WebSocket'],
      icon: <ClipboardDocumentListIcon className="w-12 h-12" />,
      gradient: 'from-purple-500 to-pink-500',
      github: '#',
      demo: '#'
    },
    {
      title: 'Data Analytics Dashboard',
      description: 'Interactive dashboard for data visualization and analytics with real-time charts and reporting features.',
      technologies: ['Python', 'Django', 'Chart.js', 'PostgreSQL'],
      icon: <ChartBarIcon className="w-12 h-12" />,
      gradient: 'from-green-500 to-teal-500',
      github: '#',
      demo: '#'
    },
    {
      title: 'Social Media App',
      description: 'Modern social media platform with real-time messaging, post sharing, and user interactions.',
      technologies: ['React Native', 'Firebase', 'Redux', 'Socket.io'],
      icon: <CodeBracketIcon className="w-12 h-12" />,
      gradient: 'from-orange-500 to-red-500',
      github: '#',
      demo: '#'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  return (
    <section id="projects" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <motion.h2 variants={itemVariants} className="section-title">
            Featured Projects
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.3 }}
                className="project-card group"
              >
                {/* Project Image/Icon */}
                <div className={`h-48 bg-gradient-to-br ${project.gradient} flex items-center justify-center text-white relative overflow-hidden`}>
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.3 }}
                  >
                    {project.icon}
                  </motion.div>
                  
                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                    <motion.a
                      href={project.github}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-white text-gray-800 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-2"
                    >
                      <CodeBracketIcon className="w-4 h-4" />
                      <span>Code</span>
                    </motion.a>
                    <motion.a
                      href={project.demo}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-blue-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-600 transition-colors duration-200 flex items-center space-x-2"
                    >
                      <ArrowTopRightOnSquareIcon className="w-4 h-4" />
                      <span>Demo</span>
                    </motion.a>
                  </div>
                </div>
                
                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-200">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed mb-4">
                    {project.description}
                  </p>
                  
                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors duration-200"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  
                  {/* Links */}
                  <div className="flex space-x-4">
                    <a
                      href={project.github}
                      className="text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200 flex items-center space-x-1"
                    >
                      <CodeBracketIcon className="w-4 h-4" />
                      <span>GitHub</span>
                    </a>
                    <a
                      href={project.demo}
                      className="text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200 flex items-center space-x-1"
                    >
                      <ArrowTopRightOnSquareIcon className="w-4 h-4" />
                      <span>Live Demo</span>
                    </a>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
          
          {/* View More Projects Button */}
          <motion.div
            variants={itemVariants}
            className="text-center mt-12"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn btn-primary px-8 py-3"
            >
              View More Projects
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Projects
